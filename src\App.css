@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Windows 11 Design System */
:root {
  /* Windows 11 System Colors */
  --windows-bg: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  --windows-control-bg: rgba(255, 255, 255, 0.8);
  --windows-border: rgba(0, 0, 0, 0.15);
  --windows-active-border: #0078d4;
  --windows-text: #1f2937;
  --windows-button-bg: #ffffff;
  --windows-button-hover: #f0f9ff;
  --windows-button-active: #e0f2fe;
  --windows-menu-bg: rgba(255, 255, 255, 0.9);
  --windows-menu-hover: rgba(0, 120, 212, 0.1);
  --windows-titlebar: #0078d4;
  --windows-shadow: rgba(0, 0, 0, 0.2);
  
  /* Windows 11 Backdrop Blur Support */
  --backdrop-blur-sm: blur(4px);
  --backdrop-blur-md: blur(8px);
  --backdrop-blur-lg: blur(16px);
  --backdrop-blur-xl: blur(24px);
  
  /* Modern Border Radius */
  --radius: 0.5rem;
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 211 100% 50%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 211 100% 50%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  
  /* Dark Windows Colors */
  --windows-bg: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  --windows-control-bg: rgba(0, 0, 0, 0.5);
  --windows-border: rgba(255, 255, 255, 0.2);
  --windows-active-border: #60a5fa;
  --windows-text: #f9fafb;
  --windows-button-bg: #374151;
  --windows-button-hover: #4b5563;
  --windows-button-active: #6b7280;
  --windows-menu-bg: rgba(0, 0, 0, 0.8);
  --windows-menu-hover: rgba(96, 165, 250, 0.2);
  --windows-titlebar: #3b82f6;
  --windows-shadow: rgba(0, 0, 0, 0.5);
}

/* Windows Font Family */
.font-segoe {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Base Windows styling */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 12px;
  background-color: var(--windows-bg);
  color: var(--windows-text);
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Windows Button Styles */
.windows-button {
  background: var(--windows-button-bg) !important;
  border: 1px solid var(--windows-border) !important;
  color: var(--windows-text) !important;
  font-family: 'Segoe UI', sans-serif !important;
  font-size: 11px !important;
  padding: 6px 12px !important;
  min-height: 23px !important;
  cursor: pointer !important;
  border-radius: 3px !important;
  transition: all 0.1s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.windows-button:hover:not(:disabled) {
  background: var(--windows-button-hover) !important;
  border-color: var(--windows-active-border) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
}

.windows-button:active:not(:disabled) {
  background: var(--windows-button-active) !important;
  border-color: var(--windows-active-border) !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.windows-button:disabled {
  background: #f5f5f5 !important;
  color: #6d6d6d !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.windows-button-primary {
  background: var(--windows-titlebar) !important;
  color: white !important;
  border-color: #0066cc !important;
  box-shadow: 0 1px 3px rgba(0, 120, 212, 0.3) !important;
}

.windows-button-primary:hover:not(:disabled) {
  background: #106ebe !important;
  box-shadow: 0 2px 5px rgba(0, 120, 212, 0.4) !important;
}

.windows-button-danger {
  background: #dc3545 !important;
  color: white !important;
  border-color: #c82333 !important;
  box-shadow: 0 1px 3px rgba(220, 53, 69, 0.3) !important;
}

.windows-button-danger:hover:not(:disabled) {
  background: #c82333 !important;
  box-shadow: 0 2px 5px rgba(220, 53, 69, 0.4) !important;
}

/* Additional button visibility utilities */
.btn-visible {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.btn-visible:hover {
  background-color: #f9fafb !important;
  border-color: #0078d4 !important;
}

.btn-primary-visible {
  background-color: #0078d4 !important;
  border: 1px solid #0066cc !important;
  color: white !important;
}

.btn-primary-visible:hover {
  background-color: #106ebe !important;
}

/* Force button text visibility */
.windows-app button {
  color: var(--windows-text) !important;
}

.windows-app .windows-button-primary {
  color: white !important;
}

/* Windows Input Styles */
.windows-input {
  height: 21px !important;
  min-height: 21px !important;
  padding: 2px 4px !important;
  font-size: 11px !important;
  border: 1px solid var(--windows-border) !important;
  background: white !important;
  border-radius: 0 !important;
  outline: none !important;
  box-shadow: none !important;
}

.windows-input:focus {
  border-color: var(--windows-active-border) !important;
  box-shadow: none !important;
  outline: none !important;
}

.windows-input:disabled {
  background: var(--windows-control-bg);
  color: #6d6d6d;
}

/* Windows Menu Styles */
.windows-menu {
  background: var(--windows-menu-bg);
  border: 1px solid var(--windows-border);
  box-shadow: 2px 2px 4px var(--windows-shadow);
  font-size: 11px;
  padding: 2px 0;
}

.windows-menu-item {
  padding: 4px 20px 4px 8px;
  cursor: pointer;
  white-space: nowrap;
}

.windows-menu-item:hover {
  background: var(--windows-menu-hover);
}

.windows-menu-separator {
  height: 1px;
  background: var(--windows-border);
  margin: 2px 0;
}

/* Windows Toolbar Styles */
.windows-toolbar {
  background: var(--windows-control-bg);
  border-bottom: 1px solid var(--windows-border);
  padding: 2px;
}

.windows-toolbar-button {
  background: transparent;
  border: 1px solid transparent;
  padding: 3px;
  margin: 1px;
  cursor: pointer;
  border-radius: 0;
}

.windows-toolbar-button:hover {
  background: var(--windows-button-hover);
  border-color: var(--windows-border);
}

.windows-toolbar-button:active {
  background: var(--windows-button-active);
  border-color: var(--windows-active-border);
}

/* Windows Secondary Toolbar */
.windows-toolbar-secondary {
  background: var(--windows-menu-bg);
  border-bottom: 1px solid var(--windows-border);
  padding: 6px 8px;
  font-size: 11px;
}

/* Windows Content Area */
.windows-content {
  padding: 8px;
  background: var(--windows-bg);
  height: 100%;
  overflow-y: auto;
}

/* Windows Title and Subtitle */
.windows-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--windows-text);
  margin: 0;
}

.windows-subtitle {
  font-size: 11px;
  color: #666;
  margin: 2px 0 0 0;
}

/* Windows Section Title */
.windows-section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--windows-text);
  margin: 0;
}

/* Windows Label */
.windows-label {
  font-size: 11px;
  font-weight: normal;
  color: var(--windows-text);
  display: block;
  margin-bottom: 2px;
}

/* Windows Select */
.windows-select {
  height: 21px !important;
  min-height: 21px !important;
  padding: 2px 4px !important;
  font-size: 11px !important;
  border: 1px solid var(--windows-border) !important;
  background: white !important;
  border-radius: 0 !important;
  outline: none !important;
  box-shadow: none !important;
}

.windows-select:focus {
  border-color: var(--windows-active-border) !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Windows Tree View */
.windows-tree {
  background: white;
  border: 1px solid var(--windows-border);
  font-size: 11px;
}

.windows-tree-item {
  padding: 1px 16px 1px 4px;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.windows-tree-item:hover {
  background: var(--windows-menu-hover);
}

.windows-tree-item.selected {
  background: var(--windows-active-border);
  color: white;
}

/* Windows Status Bar */
.windows-statusbar {
  background: var(--windows-control-bg);
  border-top: 1px solid var(--windows-border);
  font-size: 11px;
  height: 22px;
}

/* Windows Dialog Styles */
.windows-dialog {
  border: 1px solid var(--windows-border);
  background-color: var(--windows-control-bg);
  box-shadow: 0px 4px 12px var(--windows-shadow);
}

.windows-dialog-title {
  background-color: var(--windows-titlebar);
  color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Windows Control Buttons */
.windows-control-button {
  transition: background-color 0.1s ease;
}

.windows-control-button.close-button:hover {
  background-color: #e81123;
}

.windows-control-button.minimize-button:hover,
.windows-control-button.maximize-button:hover {
  background-color: rgba(229, 229, 229, 0.5);
}

/* Windows Title Bar */
.windows-title-bar {
  height: 32px;
  background-color: var(--windows-titlebar);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.5rem 0 0.75rem;
  -webkit-app-region: drag;
}

.window-title-bar {
  -webkit-app-region: drag;
}

.windows-title-bar-controls {
  -webkit-app-region: no-drag;
}

.windows-title-bar-title {
  font-size: 0.75rem;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Context Menu */
.windows-context-menu {
  background-color: white;
  border: 1px solid var(--windows-border);
  box-shadow: 0px 4px 12px var(--windows-shadow);
  min-width: 200px;
  z-index: 50;
}

.windows-context-menu-item {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: default;
}

.windows-context-menu-item:hover {
  background-color: var(--windows-menu-hover);
}

.windows-context-menu-separator {
  height: 1px;
  background-color: var(--windows-border);
  margin: 0.25rem 0;
}

/* Override Tailwind base styles for Windows look */
.windows-app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.windows-app * {
  border-radius: 0 !important;
}

.windows-app input,
.windows-app button,
.windows-app select,
.windows-app textarea {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-size: 11px !important;
}

/* Remove modern web styling */
.windows-app .shadow-sm,
.windows-app .shadow,
.windows-app .shadow-md,
.windows-app .shadow-lg {
  box-shadow: none !important;
}

/* Scrollbar styling for Windows look */
.windows-scrollbar::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

.windows-scrollbar::-webkit-scrollbar-track {
  background: var(--windows-control-bg);
}

.windows-scrollbar::-webkit-scrollbar-thumb {
  background: var(--windows-border);
  border: 1px solid #999;
}

.windows-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.windows-scrollbar::-webkit-scrollbar-corner {
  background: var(--windows-control-bg);
}

/* Windows 11 Modern Utility Classes */
.windows-11-glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.windows-11-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.windows-11-button {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.windows-11-button:hover {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.windows-11-navigation {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.windows-11-toolbar {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.windows-11-statusbar {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(16px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.windows-11-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.windows-11-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
}

/* Modern Focus Styles */
.windows-11-focus:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Modern Scrollbar for Windows 11 */
.windows-11-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.windows-11-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.windows-11-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.windows-11-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animation Classes */
.windows-11-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.windows-11-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Windows 11 Mica Effect */
.windows-11-mica {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(20px);
}

/* Reset Tailwind button defaults that might interfere */
@layer base {
  button {
    background-color: unset;
    border: unset;
    color: unset;
  }
}

/* Ensure windows buttons always show */
button.windows-button,
button.windows-button-primary,
.windows-button,
.windows-button-primary {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  opacity: 1 !important;
  visibility: visible !important;
}
