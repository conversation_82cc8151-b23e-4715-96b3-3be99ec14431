import { useState } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';

export default function WindowControls() {
  const [isMaximized, setIsMaximized] = useState(false);

  const handleMinimize = async () => {
    const appWindow = await getCurrentWindow();
    await appWindow.minimize();
  };

  const handleMaximizeRestore = async () => {
    const appWindow = await getCurrentWindow();
    const isCurrentlyMaximized = await appWindow.isMaximized();
    if (isCurrentlyMaximized) {
      await appWindow.unmaximize();
      setIsMaximized(false);
    } else {
      await appWindow.maximize();
      setIsMaximized(true);
    }
  };

  const handleClose = async () => {
    const appWindow = await getCurrentWindow();
    await appWindow.close();
  };
  return (
    <div className="flex items-center windows-title-bar-controls">
      <button
        className="h-8 w-12 flex items-center justify-center hover:bg-white/20 hover:backdrop-blur-sm transition-all duration-200 rounded-tl-none rounded-tr-none group"
        onClick={handleMinimize}
        aria-label="Minimize"
      >
        <span className="h-0.5 w-3 bg-slate-700 group-hover:bg-slate-800" />
      </button>
      <button
        className="h-8 w-12 flex items-center justify-center hover:bg-white/20 hover:backdrop-blur-sm transition-all duration-200 group"
        onClick={handleMaximizeRestore}
        aria-label={isMaximized ? "Restore" : "Maximize"}
      >
        {isMaximized ? (
          <div className="h-3 w-3 border border-slate-700 relative -mt-0.5 group-hover:border-slate-800">
            <div className="h-3 w-3 border border-slate-700 absolute -top-1 -right-1 bg-white/50 backdrop-blur-sm group-hover:border-slate-800" />
          </div>
        ) : (
          <div className="h-3 w-3 border border-slate-700 group-hover:border-slate-800" />
        )}
      </button>
      <button
        className="h-8 w-12 flex items-center justify-center hover:bg-red-500/90 hover:backdrop-blur-sm hover:text-white transition-all duration-200 rounded-tr-lg group"
        onClick={handleClose}
        aria-label="Close"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="10"
          height="10"
          viewBox="0 0 10 10"
          className="fill-current text-slate-700 group-hover:text-white"
        >
          <path d="M1.41 0L0 1.41 3.59 5 0 8.59 1.41 10 5 6.41 8.59 10 10 8.59 6.41 5 10 1.41 8.59 0 5 3.59 1.41 0z" />
        </svg>
      </button>
    </div>
  );
}
